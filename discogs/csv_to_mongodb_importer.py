#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CSV到MongoDB批量导入工具

功能：
1. 读取CSV文件中的release数据
2. 将CSV数据转换为适合MongoDB的文档格式
3. 批量插入到MongoDB的release_new集合中
4. 处理JSON字段、数据类型转换和错误处理
5. 提供进度显示和详细的统计报告

特性：
- 内存效率处理，分批读取和插入
- 完善的错误处理和重试机制
- 支持重复数据检测和处理
- 详细的日志记录和进度显示
- 支持测试模式和配置选项

作者：AI Assistant
创建时间：2025-07-29
"""

import os
import sys
import csv
import json
import time
import logging
import argparse
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
from pymongo import MongoClient
from pymongo.errors import BulkWriteError, DuplicateKeyError
import pandas as pd
import numpy as np

# 导入项目枚举
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from release.enums import Source, Permissions, Status

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
COLLECTION_NAME = 'release_new'

# 默认配置
DEFAULT_BATCH_SIZE = 100
DEFAULT_CSV_FILE = 'batch_1_releases.csv'
DEFAULT_LOG_FILE = 'csv_import_log.txt'

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(DEFAULT_LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CSVToMongoImporter:
    """CSV到MongoDB导入器"""
    
    def __init__(self, csv_file_path: str, batch_size: int = DEFAULT_BATCH_SIZE, 
                 test_mode: bool = False, skip_duplicates: bool = True):
        self.csv_file_path = csv_file_path
        self.batch_size = batch_size
        self.test_mode = test_mode
        self.skip_duplicates = skip_duplicates
        
        # 统计信息
        self.stats = {
            'total_rows': 0,
            'processed_rows': 0,
            'successful_inserts': 0,
            'failed_inserts': 0,
            'skipped_duplicates': 0,
            'validation_errors': 0,
            'start_time': None,
            'end_time': None
        }
        
        # MongoDB连接
        self.client = None
        self.db = None
        self.collection = None
        
    def connect_to_mongodb(self) -> bool:
        """连接到MongoDB并返回连接状态"""
        try:
            self.client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=30000)
            # 测试连接
            self.client.admin.command('ping')
            self.db = self.client[DB_NAME]
            self.collection = self.db[COLLECTION_NAME]
            
            # 确保集合存在
            if COLLECTION_NAME not in self.db.list_collection_names():
                self.db.create_collection(COLLECTION_NAME)
                logger.info(f"✅ 创建集合: {COLLECTION_NAME}")
            
            logger.info(f"✅ 成功连接到MongoDB: {DB_NAME}.{COLLECTION_NAME}")
            return True
            
        except Exception as e:
            logger.error(f"❌ MongoDB连接失败: {e}")
            return False
    
    def parse_json_field(self, field_value: str) -> Any:
        """解析JSON字段，处理CSV中的JSON字符串"""
        if not field_value or field_value.strip() == '':
            return []

        try:
            # 处理CSV中的双引号转义
            # CSV格式：字段被双引号包围，内部的双引号被转义为""
            if field_value.startswith('"') and field_value.endswith('"'):
                field_value = field_value[1:-1]

            # 处理CSV转义的双引号

            # 1. 先处理特殊的引号+反斜杠模式
            # ""12\"" -> "12\""（在JSON中正确表示包含双引号的字符串）
            # 使用临时标记避免后续双引号处理的干扰
            field_value = field_value.replace('""12\""""', '"12__QUOTE__"')
            # 处理各种尺寸的引号模式
            field_value = field_value.replace('"12\\"', '"12__QUOTE__"')
            field_value = field_value.replace('"10\\"', '"10__QUOTE__"')
            field_value = field_value.replace('"7\\"', '"7__QUOTE__"')
            # 处理方括号内的引号问题
            import re

            # 2. 先处理普通的双引号转义，但保护特殊标记
            field_value = field_value.replace('""', '"')

            # 3. 修复不完整的空字符串值和未终止的字符串
            # 先处理最常见的空字符串模式: ": ", -> ": "",
            field_value = re.sub(r':\s*"\s*,', r': "",', field_value)
            # 处理字符串末尾的空值: ": "} -> ": ""}
            field_value = re.sub(r':\s*"\s*}', r': ""}', field_value)
            # 处理数组末尾的空值: ": "] -> ": ""]
            field_value = re.sub(r':\s*"\s*]', r': ""]', field_value)

            # 修复未终止的字符串 - 更精确的模式
            # 匹配 "key": "value} 并修复为 "key": "value"}
            field_value = re.sub(r':\s*"([^"]*)"?\s*}', r': "\1"}', field_value)
            # 匹配 "key": "value] 并修复为 "key": "value"]
            field_value = re.sub(r':\s*"([^"]*)"?\s*]', r': "\1"]', field_value)

            # 4. 恢复特殊标记为正确的JSON转义
            field_value = field_value.replace('__QUOTE__', '\\"')

            # 解析JSON
            parsed = json.loads(field_value)
            return parsed if parsed is not None else []

        except (json.JSONDecodeError, ValueError) as e:
            logger.warning(f"⚠️ JSON解析失败: {field_value[:100]}... 错误: {e}")
            return []

    def clean_text_field(self, text: str) -> str:
        """清理文本字段中的换行符和特殊字符"""
        if not text or pd.isna(text):
            return ""

        # 规范化换行符
        text = str(text).replace('\r\n', '\n').replace('\r', '\n')
        # 保留换行符，但确保它们不会破坏CSV结构
        return text.strip()

    def convert_csv_row_to_document(self, row: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """将CSV行转换为MongoDB文档"""
        try:
            # 基础字段转换
            doc = {
                'id': int(row['id']) if row['id'] else None,
                'y_id': row['y_id'].strip() if row['y_id'] else None,
                'title': row['title'].strip() if row['title'] else None,
                'country': row['country'].strip() if row['country'] else None,
                'year': int(row['year']) if row['year'] and row['year'].isdigit() else None,
                'notes': self.clean_text_field(row['notes']) if row['notes'] else None,
                'discogs_status': row['discogs_status'].strip() if row['discogs_status'] else None,
            }
            
            # JSON数组字段转换
            json_fields = ['artists', 'extra_artists', 'labels', 'companies', 
                          'formats', 'genres', 'styles', 'identifiers', 'tracklist', 'images']
            
            for field in json_fields:
                if field in row:
                    doc[field] = self.parse_json_field(row[field])
            
            # 数值字段转换
            if row.get('master_id') and row['master_id'].isdigit():
                doc['master_id'] = int(row['master_id'])
            else:
                doc['master_id'] = None
            
            # 权限和状态字段
            doc['images_permissions'] = int(row['images_permissions']) if row.get('images_permissions') else Permissions.ALL_VISIBLE.value
            doc['permissions'] = int(row['permissions']) if row.get('permissions') else Permissions.ALL_VISIBLE.value
            doc['source'] = int(row['source']) if row.get('source') else Source.DISCOGS.value
            
            # 时间戳字段
            if row.get('created_at'):
                try:
                    doc['created_at'] = datetime.fromisoformat(row['created_at'].replace('Z', '+00:00'))
                except ValueError:
                    doc['created_at'] = datetime.now(timezone.utc)
            else:
                doc['created_at'] = datetime.now(timezone.utc)
            
            if row.get('updated_at'):
                try:
                    doc['updated_at'] = datetime.fromisoformat(row['updated_at'].replace('Z', '+00:00'))
                except ValueError:
                    doc['updated_at'] = datetime.now(timezone.utc)
            else:
                doc['updated_at'] = datetime.now(timezone.utc)
            
            return doc
            
        except Exception as e:
            logger.error(f"❌ 行转换失败: {e}, 行数据: {row}")
            self.stats['validation_errors'] += 1
            return None
    
    def validate_document(self, doc: Dict[str, Any]) -> bool:
        """验证文档是否包含必要字段"""
        required_fields = ['id', 'y_id']
        
        for field in required_fields:
            if not doc.get(field):
                logger.warning(f"⚠️ 缺少必要字段 {field}: {doc}")
                return False
        
        return True
    
    def batch_insert_documents(self, documents: List[Dict[str, Any]]) -> tuple:
        """批量插入文档，返回(成功数量, 失败数量)"""
        if not documents:
            return 0, 0
        
        success_count = 0
        error_count = 0
        
        try:
            if self.skip_duplicates:
                # 使用insert_many with ordered=False来跳过重复项
                result = self.collection.insert_many(documents, ordered=False)
                success_count = len(result.inserted_ids)
                
            else:
                # 逐个插入以处理重复项
                for doc in documents:
                    try:
                        self.collection.insert_one(doc)
                        success_count += 1
                    except DuplicateKeyError:
                        self.stats['skipped_duplicates'] += 1
                        logger.debug(f"跳过重复记录: ID={doc.get('id')}")
                    except Exception as e:
                        error_count += 1
                        logger.error(f"❌ 单条插入失败: {e}")
                        
        except BulkWriteError as e:
            # 处理批量写入错误
            success_count = e.details.get('nInserted', 0)
            error_count = len(documents) - success_count
            
            # 统计重复键错误
            for error in e.details.get('writeErrors', []):
                if error.get('code') == 11000:  # 重复键错误
                    self.stats['skipped_duplicates'] += 1
                    error_count -= 1  # 重复不算作错误
                    
            logger.info(f"批量插入部分成功: 成功 {success_count}, 重复 {self.stats['skipped_duplicates']}")
            
        except Exception as e:
            logger.error(f"❌ 批量插入失败: {e}")
            error_count = len(documents)
        
        return success_count, error_count

    def get_csv_row_count(self) -> int:
        """获取CSV文件的总行数（不包括标题行）"""
        try:
            # 使用pandas快速获取行数
            df = pd.read_csv(
                self.csv_file_path,
                encoding='utf-8',
                quoting=csv.QUOTE_ALL,
                na_filter=False,
                dtype=str
            )
            return len(df)
        except Exception as e:
            logger.error(f"❌ 无法读取CSV文件行数: {e}")
            return 0

    def process_csv_file(self) -> bool:
        """处理CSV文件的主函数"""
        logger.info("🚀 开始CSV到MongoDB导入过程")
        logger.info("=" * 60)

        # 检查CSV文件是否存在
        if not os.path.exists(self.csv_file_path):
            logger.error(f"❌ CSV文件不存在: {self.csv_file_path}")
            return False

        # 连接数据库
        if not self.connect_to_mongodb():
            return False

        if self.test_mode:
            logger.info("🧪 测试模式已启用，将只处理前100行")

        # 开始处理
        self.stats['start_time'] = datetime.now()
        batch_documents = []

        try:
            # 使用pandas读取CSV文件，能更好地处理包含换行符的字段
            logger.info("📖 使用pandas读取CSV文件...")
            df = pd.read_csv(
                self.csv_file_path,
                encoding='utf-8',
                quoting=csv.QUOTE_ALL,  # 处理包含换行符的字段
                na_filter=False,  # 防止空字符串被转换为NaN
                dtype=str  # 所有字段先读取为字符串
            )

            # 更新总行数
            self.stats['total_rows'] = len(df)
            logger.info(f"📊 使用pandas读取CSV文件，总行数: {self.stats['total_rows']:,}")

            for row_num, (_, row) in enumerate(df.iterrows(), 1):
                # 测试模式限制
                if self.test_mode and row_num > 100:
                    break

                # 将pandas Series转换为字典
                row_dict = row.to_dict()

                # 转换行数据
                doc = self.convert_csv_row_to_document(row_dict)
                if doc and self.validate_document(doc):
                    batch_documents.append(doc)
                    self.stats['processed_rows'] += 1

                # 批量插入
                if len(batch_documents) >= self.batch_size:
                    success, error = self.batch_insert_documents(batch_documents)
                    self.stats['successful_inserts'] += success
                    self.stats['failed_inserts'] += error

                    # 显示进度
                    progress = (self.stats['processed_rows'] / self.stats['total_rows']) * 100
                    logger.info(f"📈 进度: {self.stats['processed_rows']:,}/"
                              f"{self.stats['total_rows']:,} ({progress:.1f}%) - "
                              f"成功: {self.stats['successful_inserts']:,}")

                    batch_documents = []

                # 处理剩余的文档
                if batch_documents:
                    success, error = self.batch_insert_documents(batch_documents)
                    self.stats['successful_inserts'] += success
                    self.stats['failed_inserts'] += error

        except Exception as e:
            logger.error(f"❌ 处理CSV文件时发生错误: {e}")
            return False

        finally:
            self.stats['end_time'] = datetime.now()
            if self.client:
                self.client.close()

        # 显示最终统计
        self.print_final_stats()
        return True

    def print_final_stats(self):
        """打印最终统计信息"""
        duration = self.stats['end_time'] - self.stats['start_time']

        logger.info("\n" + "=" * 60)
        logger.info("📊 导入完成 - 最终统计")
        logger.info("=" * 60)
        logger.info(f"📁 CSV文件: {self.csv_file_path}")
        logger.info(f"⏱️  处理时间: {duration}")
        logger.info(f"📊 总行数: {self.stats['total_rows']:,}")
        logger.info(f"✅ 处理行数: {self.stats['processed_rows']:,}")
        logger.info(f"✅ 成功插入: {self.stats['successful_inserts']:,}")
        logger.info(f"❌ 插入失败: {self.stats['failed_inserts']:,}")
        logger.info(f"⚠️  跳过重复: {self.stats['skipped_duplicates']:,}")
        logger.info(f"⚠️  验证错误: {self.stats['validation_errors']:,}")

        if self.stats['processed_rows'] > 0:
            success_rate = (self.stats['successful_inserts'] / self.stats['processed_rows']) * 100
            logger.info(f"📈 成功率: {success_rate:.1f}%")

        logger.info("=" * 60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CSV到MongoDB批量导入工具')
    parser.add_argument('--csv-file', '-f', default=DEFAULT_CSV_FILE,
                       help=f'CSV文件路径 (默认: {DEFAULT_CSV_FILE})')
    parser.add_argument('--batch-size', '-b', type=int, default=DEFAULT_BATCH_SIZE,
                       help=f'批量插入大小 (默认: {DEFAULT_BATCH_SIZE})')
    parser.add_argument('--test-mode', '-t', action='store_true',
                       help='测试模式，只处理前100行')
    parser.add_argument('--allow-duplicates', '-d', action='store_true',
                       help='允许重复数据（默认跳过重复）')
    parser.add_argument('--log-file', '-l', default=DEFAULT_LOG_FILE,
                       help=f'日志文件路径 (默认: {DEFAULT_LOG_FILE})')

    args = parser.parse_args()

    # 更新日志文件配置
    if args.log_file != DEFAULT_LOG_FILE:
        # 重新配置日志处理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(args.log_file, encoding='utf-8'),
                logging.StreamHandler()
            ],
            force=True
        )

    # 创建导入器实例
    importer = CSVToMongoImporter(
        csv_file_path=args.csv_file,
        batch_size=args.batch_size,
        test_mode=args.test_mode,
        skip_duplicates=not args.allow_duplicates
    )

    # 执行导入
    success = importer.process_csv_file()

    if success:
        logger.info("🎉 CSV导入完成！")
        sys.exit(0)
    else:
        logger.error("❌ CSV导入失败！")
        sys.exit(1)


if __name__ == '__main__':
    main()
